2025-07-08 04:38:36,886 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 33 messages from #📝-bot-logs (keep_pinned: True)
2025-07-08 04:41:21,613 - __main__ - WARNING - Slow command execution: watchlist took 7.54s
2025-07-08 04:53:49,886 - handlers.discord.alerts.trading_time_alerts - WARNING - Channel time-trade has reached maximum pins, cannot pin market_session_status
2025-07-08 04:53:50,676 - handlers.discord.alerts.trading_time_alerts - WARNING - Channel time-trade has reached maximum pins, cannot pin market_overview
2025-07-08 04:53:52,032 - handlers.discord.alerts.trading_time_alerts - ERROR - Error managing pin limit: 400 Bad Request (error code: 30003): Maximum number of pins reached (50)
2025-07-08 04:54:11,552 - __main__ - WARNING - Slow command execution: watchlist took 7.56s
2025-07-08 05:51:18,404 - handlers.discord.alerts.trading_time_alerts - WARNING - Channel time-trade has reached maximum pins, cannot pin market_session_status
2025-07-08 05:51:19,108 - handlers.discord.alerts.trading_time_alerts - WARNING - Channel time-trade has reached maximum pins, cannot pin market_overview
2025-07-08 05:51:20,482 - handlers.discord.alerts.trading_time_alerts - ERROR - Error managing pin limit: 400 Bad Request (error code: 30003): Maximum number of pins reached (50)
2025-07-08 05:51:32,463 - __main__ - WARNING - Slow command execution: watchlist took 10.58s
2025-07-08 05:54:02,067 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 191 messages from #🚨-alerts (keep_pinned: False)
2025-07-08 05:54:32,661 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391057210028593152 responded with 429. Retrying in 0.63 seconds.
2025-07-08 05:54:33,913 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391057208720097422 responded with 429. Retrying in 0.38 seconds.
2025-07-08 05:54:35,041 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391046149494018168 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:54:35,937 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391044898605633661 responded with 429. Retrying in 0.35 seconds.
2025-07-08 05:54:36,897 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391042377010909245 responded with 429. Retrying in 0.39 seconds.
2025-07-08 05:54:37,918 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391039884587696169 responded with 429. Retrying in 0.37 seconds.
2025-07-08 05:54:38,954 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391039882654384188 responded with 429. Retrying in 0.34 seconds.
2025-07-08 05:54:39,894 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391039881345761384 responded with 429. Retrying in 0.40 seconds.
2025-07-08 05:54:41,026 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391039880267825293 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:54:41,934 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391039878434656300 responded with 429. Retrying in 0.36 seconds.
2025-07-08 05:54:42,896 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391039487651348602 responded with 429. Retrying in 0.39 seconds.
2025-07-08 05:54:43,877 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391036993831698493 responded with 429. Retrying in 0.41 seconds.
2025-07-08 05:54:44,892 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391036993286180956 responded with 429. Retrying in 0.40 seconds.
2025-07-08 05:54:45,882 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391036992053055632 responded with 429. Retrying in 0.41 seconds.
2025-07-08 05:54:47,030 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391036991914639452 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:54:47,944 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391036990358687765 responded with 429. Retrying in 0.35 seconds.
2025-07-08 05:54:48,954 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391032826316718231 responded with 429. Retrying in 0.34 seconds.
2025-07-08 05:54:49,946 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1391012696794595439 responded with 429. Retrying in 0.34 seconds.
2025-07-08 05:54:50,935 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390997597266903081 responded with 429. Retrying in 0.36 seconds.
2025-07-08 05:54:51,910 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390990047242616904 responded with 429. Retrying in 0.38 seconds.
2025-07-08 05:54:52,916 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390985010760257627 responded with 429. Retrying in 0.37 seconds.
2025-07-08 05:54:53,909 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390979977641459752 responded with 429. Retrying in 0.39 seconds.
2025-07-08 05:54:54,981 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390977462451568670 responded with 429. Retrying in 0.31 seconds.
2025-07-08 05:54:56,194 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390974968157966359 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:54:57,219 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390974965309771807 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:54:58,154 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390967393370509473 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:54:59,112 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390967390275239936 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:00,028 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390964172212142103 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:00,988 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1390964168517091400 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:01,977 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1389969536148701365 responded with 429. Retrying in 0.31 seconds.
2025-07-08 05:55:02,896 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1389969534601134192 responded with 429. Retrying in 0.41 seconds.
2025-07-08 05:55:03,956 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388881292283609118 responded with 429. Retrying in 0.33 seconds.
2025-07-08 05:55:04,879 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388881289221767249 responded with 429. Retrying in 0.41 seconds.
2025-07-08 05:55:06,038 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388879994427805859 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:06,990 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388879991265300602 responded with 429. Retrying in 0.31 seconds.
2025-07-08 05:55:07,903 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388875075159326820 responded with 429. Retrying in 0.39 seconds.
2025-07-08 05:55:08,920 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388875072026054678 responded with 429. Retrying in 0.40 seconds.
2025-07-08 05:55:09,951 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388517742453457028 responded with 429. Retrying in 0.34 seconds.
2025-07-08 05:55:10,953 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388517738166747196 responded with 429. Retrying in 0.34 seconds.
2025-07-08 05:55:11,922 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388509836085563483 responded with 429. Retrying in 0.36 seconds.
2025-07-08 05:55:12,958 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388509832998289418 responded with 429. Retrying in 0.34 seconds.
2025-07-08 05:55:13,958 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388508923249688606 responded with 429. Retrying in 0.33 seconds.
2025-07-08 05:55:14,946 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388508921718902885 responded with 429. Retrying in 0.35 seconds.
2025-07-08 05:55:16,034 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388000478612164618 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:16,988 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1388000475650981959 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:21,869 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387995419019317299 responded with 429. Retrying in 0.42 seconds.
2025-07-08 05:55:22,944 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1387995416867635292 responded with 429. Retrying in 0.35 seconds.
2025-07-08 05:55:23,899 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381212049723625634 responded with 429. Retrying in 0.39 seconds.
2025-07-08 05:55:25,089 - discord.http - WARNING - We are being rate limited. DELETE https://discord.com/api/v10/channels/1376551221711732796/messages/1381212048356151316 responded with 429. Retrying in 0.30 seconds.
2025-07-08 05:55:26,066 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 162 messages from #time-trade (keep_pinned: False)
2025-07-08 06:01:19,298 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-07-08 06:51:13,181 - handlers.discord.alerts.trading_time_alerts - WARNING - Persistent market_session_status message not found, creating new one
2025-07-08 08:08:42,022 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-07-08 08:09:25,702 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 85, in update_all_data
    positions_result = self.trading_service.get_positions()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 342, in get_positions
    positions = self.exchange.fetch_positions()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10139, in fetch_positions
    return self.fetch_positions_risk(symbols, params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 10309, in fetch_positions_risk
    response = self.fapiPrivateV3GetPositionRisk(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 337, in begin
    self.headers = self.msg = parse_headers(self.fp)
  File "/usr/lib/python3.10/http/client.py", line 236, in parse_headers
    return email.parser.Parser(_class=_class).parsestr(hstring)
  File "/usr/lib/python3.10/email/parser.py", line 67, in parsestr
    return self.parse(StringIO(text), headersonly=headersonly)
  File "/usr/lib/python3.10/email/parser.py", line 56, in parse
    feedparser.feed(data)
  File "/usr/lib/python3.10/email/feedparser.py", line 176, in feed
    self._call_parse()
  File "/usr/lib/python3.10/email/feedparser.py", line 180, in _call_parse
    self._parse()
  File "/usr/lib/python3.10/email/feedparser.py", line 240, in _parsegen
    self._parse_headers(headers)
  File "/usr/lib/python3.10/email/feedparser.py", line 488, in _parse_headers
    self._cur.set_raw(*self.policy.header_source_parse(lastvalue))
  File "/usr/lib/python3.10/email/_policybase.py", line 311, in header_source_parse
    return (name, value.rstrip('\r\n'))

2025-07-08 09:51:16,143 - handlers.discord.alerts.trading_time_alerts - WARNING - Persistent market_overview message not found, creating new one
2025-07-08 13:29:14,568 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #📈-market-data (keep_pinned: False)
2025-07-08 13:29:35,260 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 15 messages from #time-trade (keep_pinned: False)
2025-07-08 13:29:49,658 - __main__ - WARNING - Slow command execution: watchlist took 9.38s
2025-07-08 13:30:44,676 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 128, in update_all_data
    orders_result = self.trading_service.get_open_orders()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 309, in get_open_orders
    orders = self.exchange.fetch_open_orders(formatted_symbol)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 6739, in fetch_open_orders
    response = self.fapiPrivateGetOpenOrders(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-08 13:32:23,453 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:32:49,043 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:33:19,850 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 30 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:33:33,745 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 40 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:33:50,292 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 50 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:34:09,951 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 60 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:34:11,749 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5736372d4fdba6e781359706cc6fb0b4f8536a7bafed01a52f0a1de0a23f555b
2025-07-08 13:34:11,903 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5736372d4fdba6e781359706cc6fb0b4f8536a7bafed01a52f0a1de0a23f555b (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5736372d4fdba6e781359706cc6fb0b4f8536a7bafed01a52f0a1de0a23f555b)
2025-07-08 13:34:20,046 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 70 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 283, in start_monitoring
    await self.check_price_movements()
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 215, in check_price_movements
    price_data = await self.calculate_price_change(symbol, timeframe)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 144, in calculate_price_change
    ohlcv_data = await self.fetch_ohlcv_data(symbol, timeframe, limit=2)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 117, in fetch_ohlcv_data
    ohlcv = self.exchange.fetch_ohlcv(symbol, exchange_timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 383, in _make_request
    self._validate_conn(conn)
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 1017, in _validate_conn
    conn.connect()
  File "/usr/lib/python3/dist-packages/urllib3/connection.py", line 411, in connect
    self.sock = ssl_wrap_socket(
  File "/usr/lib/python3/dist-packages/urllib3/util/ssl_.py", line 402, in ssl_wrap_socket
    context.load_verify_locations(ca_certs, ca_cert_dir, ca_cert_data)

2025-07-08 13:34:31,579 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 80 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 283, in start_monitoring
    await self.check_price_movements()
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 215, in check_price_movements
    price_data = await self.calculate_price_change(symbol, timeframe)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 144, in calculate_price_change
    ohlcv_data = await self.fetch_ohlcv_data(symbol, timeframe, limit=2)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 117, in fetch_ohlcv_data
    ohlcv = self.exchange.fetch_ohlcv(symbol, exchange_timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 719, in send
    return self.build_response(request, resp)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 388, in build_response
    extract_cookies_to_jar(response.cookies, req, resp)
  File "/usr/local/lib/python3.10/dist-packages/requests/structures.py", line 44, in __init__
    self.update(data, **kwargs)
  File "/usr/lib/python3.10/_collections_abc.py", line 1006, in update
    for key, value in kwds.items():
  File "/usr/lib/python3/dist-packages/urllib3/_collections.py", line 158, in __getitem__
    return ", ".join(val[1:])

2025-07-08 13:34:48,053 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 90 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/volume_alert_service.py", line 82, in _monitor_loop
    await self._check_volume_alerts()
  File "/root/chartfix/services/market/volume_alert_service.py", line 104, in _check_volume_alerts
    h4_alert = await self._check_symbol_volume(symbol, '4h')
  File "/root/chartfix/services/market/volume_alert_service.py", line 127, in _check_symbol_volume
    df = self.market_service._fetch_ohlcv_data(symbol, timeframe, limit=21)
  File "/root/chartfix/services/market/market_service.py", line 732, in _fetch_ohlcv_data
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/frame.py", line 814, in __init__
    mgr = arrays_to_mgr(
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 152, in arrays_to_mgr
    return create_block_manager_from_column_arrays(
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/managers.py", line 2086, in create_block_manager_from_column_arrays
    blocks = _form_blocks(arrays, consolidate, refs)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/managers.py", line 2163, in _form_blocks
    blk = block_type(values, placement=BlockPlacement(placement), ndim=2)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/managers.py", line 2204, in _stack_arrays
    return stacked, placement

2025-07-08 13:35:05,343 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 100 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/volume_alert_service.py", line 82, in _monitor_loop
    await self._check_volume_alerts()
  File "/root/chartfix/services/market/volume_alert_service.py", line 109, in _check_volume_alerts
    d1_alert = await self._check_symbol_volume(symbol, '1d')
  File "/root/chartfix/services/market/volume_alert_service.py", line 127, in _check_symbol_volume
    df = self.market_service._fetch_ohlcv_data(symbol, timeframe, limit=21)
  File "/root/chartfix/services/market/market_service.py", line 732, in _fetch_ohlcv_data
    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/frame.py", line 806, in __init__
    arrays, columns, index = nested_data_to_arrays(
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 520, in nested_data_to_arrays
    arrays, columns = to_arrays(data, columns, dtype=dtype)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 845, in to_arrays
    content, columns = _finalize_columns_and_data(arr, columns, dtype)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 945, in _finalize_columns_and_data
    contents = convert_object_array(contents, dtype=dtype)
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 1068, in convert_object_array
    arrays = [convert(arr) for arr in content]
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 1068, in <listcomp>
    arrays = [convert(arr) for arr in content]
  File "/usr/local/lib/python3.10/dist-packages/pandas/core/internals/construction.py", line 1066, in convert
    return arr
  File "/usr/local/lib/python3.10/dist-packages/numpy/core/numeric.py", line 331, in full
    return a

2025-07-08 13:35:15,543 - services.market.market_service - ERROR - CoinGecko API client error: Connection timeout to host https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=false
2025-07-08 13:35:16,487 - services.market.market_monitor_service - ERROR - Error fetching P2P rates: 
2025-07-08 13:36:33,704 - services.trading.trading_service - ERROR - Network error fetching balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-07-08 13:36:33,765 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."} (Caused by: InvalidNonce: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."})
2025-07-08 13:36:41,650 - services.market.market_service - ERROR - CoinGecko API client error: Connection timeout to host https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=100&page=1&sparkline=false
2025-07-08 13:36:42,499 - services.market.market_monitor_service - ERROR - Error fetching flexible products: 
2025-07-08 13:36:48,595 - handlers.discord.trading.advanced_commands - ERROR - ❌ Error updating status message: 
2025-07-08 13:36:49,677 - discord.client - ERROR - Attempting a reconnect in 0.59s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 375, in from_client
    socket = await client.http.ws_connect(str(url))
  File "/usr/local/lib/python3.10/dist-packages/discord/http.py", line 554, in ws_connect
    return await self.__session.ws_connect(url, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/client.py", line 825, in _ws_connect
    resp = await self.request(
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/client.py", line 574, in _request
    conn = await self._connector.connect(
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/connector.py", line 544, in connect
    proto = await self._create_connection(req, traces, timeout)
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/connector.py", line 911, in _create_connection
    _, proto = await self._create_direct_connection(req, traces, timeout)
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/connector.py", line 1204, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/connector.py", line 992, in _wrap_create_connection
    return await self._loop.create_connection(*args, **kwargs)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1060, in create_connection
    sock = await self._connect_sock(
  File "/usr/lib/python3.10/asyncio/base_events.py", line 969, in _connect_sock
    await self.sock_connect(sock, address)
  File "/usr/lib/python3.10/asyncio/selector_events.py", line 501, in sock_connect
    return await fut
asyncio.exceptions.CancelledError

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.10/asyncio/tasks.py", line 456, in wait_for
    return fut.result()
asyncio.exceptions.CancelledError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 701, in connect
    self.ws = await asyncio.wait_for(coro, timeout=60.0)
  File "/usr/lib/python3.10/asyncio/tasks.py", line 458, in wait_for
    raise exceptions.TimeoutError() from exc
asyncio.exceptions.TimeoutError
2025-07-08 13:39:14,242 - services.market.economic_calendar_service - ERROR - Error fetching economic calendar: 
2025-07-08 13:39:16,342 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:39:16,559 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:39:18,798 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:40:00,048 - handlers.discord.alerts.trading_time_alerts - ERROR - Error sending trading time alert: [Errno 32] Broken pipe
2025-07-08 13:40:04,187 - discord.client - ERROR - Attempting a reconnect in 0.90s
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/discord/client.py", line 701, in connect
    self.ws = await asyncio.wait_for(coro, timeout=60.0)
  File "/usr/lib/python3.10/asyncio/tasks.py", line 445, in wait_for
    return fut.result()
  File "/usr/local/lib/python3.10/dist-packages/discord/gateway.py", line 375, in from_client
    socket = await client.http.ws_connect(str(url))
  File "/usr/local/lib/python3.10/dist-packages/discord/http.py", line 554, in ws_connect
    return await self.__session.ws_connect(url, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/client.py", line 825, in _ws_connect
    resp = await self.request(
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/client.py", line 601, in _request
    await resp.start(conn)
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/client_reqrep.py", line 965, in start
    message, payload = await protocol.read()  # type: ignore[union-attr]
  File "/usr/local/lib/python3.10/dist-packages/aiohttp/streams.py", line 622, in read
    await self._waiter
aiohttp.client_exceptions.ClientOSError: [Errno 32] Broken pipe
2025-07-08 13:40:27,615 - services.market.economic_calendar_service - ERROR - Error fetching economic calendar: 
2025-07-08 13:40:40,073 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:40:45,055 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:40:51,614 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:42:02,051 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:42:03,313 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:42:04,382 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:42:05,566 - services.market.market_monitor_service - ERROR - Error fetching P2P rates: 
2025-07-08 13:42:09,989 - services.market.market_monitor_service - ERROR - Binance API error: 400 - {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-07-08 13:44:49,357 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:45:58,583 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/trading/data_service.py", line 64, in _update_loop
    await self.update_all_data()
  File "/root/chartfix/services/trading/data_service.py", line 80, in update_all_data
    account_result = self.trading_service.get_account_balance()
  File "/root/chartfix/services/core/error_service.py", line 83, in wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/core/error_service.py", line 171, in sync_wrapper
    return func(*args, **kwargs)
  File "/root/chartfix/services/trading/trading_service.py", line 66, in get_account_balance
    balance = self.exchange.fetch_balance()
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 3613, in fetch_balance
    response = self.fapiPrivateV3GetAccount(params)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 718, in send
    extract_cookies_to_jar(self.cookies, request, r.raw)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 719, in send
    return self.build_response(request, resp)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 394, in build_response
    return response
  File "/usr/local/lib/python3.10/dist-packages/requests/cookies.py", line 137, in extract_cookies_to_jar
    jar.extract_cookies(res, req)
  File "/usr/lib/python3.10/http/cookiejar.py", line 1695, in extract_cookies
    self._cookies_lock.release()

2025-07-08 13:45:59,884 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:46:00,689 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:47:01,127 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:47:01,230 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:47:03,235 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:47:10,475 - services.market.market_monitor_service - ERROR - Binance API error: 400 - {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-07-08 13:48:04,261 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:48:04,358 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:48:06,361 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:49:06,747 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:49:06,843 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:49:08,845 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:50:09,266 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:50:09,361 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:50:11,367 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:51:11,730 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:51:11,832 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:51:13,833 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:52:10,937 - services.market.market_monitor_service - ERROR - Binance API error: 400 - {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-07-08 13:52:14,209 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:52:14,302 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:52:16,304 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:53:16,687 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:53:16,786 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:53:18,787 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:54:19,224 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:54:19,328 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:54:21,329 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:55:21,706 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:55:21,801 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:55:23,805 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:56:24,408 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:56:24,510 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:56:26,512 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:57:11,690 - services.market.market_monitor_service - ERROR - Binance API error: 400 - {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-07-08 13:57:26,918 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:57:27,008 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:57:29,011 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:58:29,401 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:58:29,494 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:58:31,497 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:59:31,942 - services.trading.trading_service - ERROR - Error fetching balance: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:59:32,051 - services.trading.trading_service - ERROR - Get positions error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 13:59:34,054 - services.trading.trading_service - ERROR - Get orders error: binance {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action"}
2025-07-08 14:00:47,786 - __main__ - WARNING - Slow command execution: watchlist took 7.58s
2025-07-08 16:36:53,167 - __main__ - ERROR - Error fetching ticker for LINKUSDT: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=LINKUSDT
2025-07-09 00:14:56,334 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-07-09 03:26:20,667 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    except Exception as e:
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 373, in watchlist_updater
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-09 03:36:53,854 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    except Exception as e:
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 283, in start_monitoring
    await self.check_price_movements()
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 215, in check_price_movements
    price_data = await self.calculate_price_change(symbol, timeframe)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 144, in calculate_price_change
    ohlcv_data = await self.fetch_ohlcv_data(symbol, timeframe, limit=2)
  File "/root/chartfix/services/market/short_term_price_alert_service.py", line 117, in fetch_ohlcv_data
    ohlcv = self.exchange.fetch_ohlcv(symbol, exchange_timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-09 04:29:56,563 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3891f852cc0d4784c8b61c8e88ff54a155e107954b0f773a816d7a320df01867
2025-07-09 04:29:56,565 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3891f852cc0d4784c8b61c8e88ff54a155e107954b0f773a816d7a320df01867 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3891f852cc0d4784c8b61c8e88ff54a155e107954b0f773a816d7a320df01867)
2025-07-09 04:32:06,628 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=909336a70e52fc560cb32dcb40fa6554ce359a6a03799f364221397c80bd1df6
2025-07-09 04:32:06,630 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=909336a70e52fc560cb32dcb40fa6554ce359a6a03799f364221397c80bd1df6 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=909336a70e52fc560cb32dcb40fa6554ce359a6a03799f364221397c80bd1df6)
2025-07-09 04:33:13,986 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    except Exception as e:
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/root/chartfix/services/market/volume_alert_service.py", line 82, in _monitor_loop
    await self._check_volume_alerts()
  File "/root/chartfix/services/market/volume_alert_service.py", line 109, in _check_volume_alerts
    d1_alert = await self._check_symbol_volume(symbol, '1d')
  File "/root/chartfix/services/market/volume_alert_service.py", line 127, in _check_symbol_volume
    df = self.market_service._fetch_ohlcv_data(symbol, timeframe, limit=21)
  File "/root/chartfix/services/market/market_service.py", line 726, in _fetch_ohlcv_data
    ohlcv = exchange.fetch_ohlcv(formatted_symbol, timeframe, limit=limit)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4501, in fetch_ohlcv
    response = self.fapiPublicGetKlines(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-09 04:35:35,507 - services.market.market_service - ERROR - Error fetching OHLCV data for LINKUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=1&symbol=LINKUSDT
2025-07-09 04:35:35,512 - services.market.market_service - WARNING - No daily candle data for LINKUSDT
2025-07-09 04:35:35,512 - services.market.market_service - WARNING - Could not get daily open price for LINKUSDT
2025-07-09 04:37:39,299 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=370b9f0ca169fda4e9ef22046e71b50a78584a361b6dcfd696b23b69f9987fe6
2025-07-09 04:37:39,301 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=370b9f0ca169fda4e9ef22046e71b50a78584a361b6dcfd696b23b69f9987fe6 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=370b9f0ca169fda4e9ef22046e71b50a78584a361b6dcfd696b23b69f9987fe6)
2025-07-09 04:42:01,368 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f01b7101867ed063e1b1d54634d6e9c607b6f8f91d0f7d7b68f84cc933ba18a5
2025-07-09 04:42:01,370 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f01b7101867ed063e1b1d54634d6e9c607b6f8f91d0f7d7b68f84cc933ba18a5 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f01b7101867ed063e1b1d54634d6e9c607b6f8f91d0f7d7b68f84cc933ba18a5)
2025-07-09 04:53:31,085 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3374ad48c454c34d5e59c6fb18d7734d6856951ce717b5a504d551709f1c0758
2025-07-09 04:53:31,089 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3374ad48c454c34d5e59c6fb18d7734d6856951ce717b5a504d551709f1c0758 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3374ad48c454c34d5e59c6fb18d7734d6856951ce717b5a504d551709f1c0758)
2025-07-09 05:18:34,160 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=56484b47cf209697cf8157013965b7e35debacc76f6e2141a5fc298ba9939290
2025-07-09 05:18:34,163 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=56484b47cf209697cf8157013965b7e35debacc76f6e2141a5fc298ba9939290 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=56484b47cf209697cf8157013965b7e35debacc76f6e2141a5fc298ba9939290)
2025-07-09 05:19:38,884 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=596d8e12f0cec8d726b1cbbdc6e199615a1d1ad79e78d2213644b1e33501abb4
2025-07-09 05:19:38,887 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=596d8e12f0cec8d726b1cbbdc6e199615a1d1ad79e78d2213644b1e33501abb4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=596d8e12f0cec8d726b1cbbdc6e199615a1d1ad79e78d2213644b1e33501abb4)
2025-07-09 05:20:11,238 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 671, in <module>
    except Exception as e:
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/discord/ext/tasks/__init__.py", line 246, in _loop
    await self.coro(*args, **kwargs)
  File "/root/chartfix/bot.py", line 373, in watchlist_updater
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 4105, in fetch_ticker
    response = self.fapiPublicGetTicker24hr(self.extend(request, params))
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/types.py", line 35, in unbound_method
    return _self.request(self.path, self.api, self.method, params, config=self.config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/binance.py", line 11330, in request
    response = self.fetch2(path, api, method, params, headers, body, config)
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 4386, in fetch2
    return self.fetch(request['url'], request['method'], request['headers'], request['body'])
  File "/usr/local/lib/python3.10/dist-packages/ccxt/base/exchange.py", line 551, in fetch
    response = self.session.request(
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 700, in urlopen
    httplib_response = self._make_request(
  File "/usr/lib/python3/dist-packages/urllib3/connectionpool.py", line 441, in _make_request
    httplib_response = conn.getresponse()
  File "/usr/lib/python3.10/http/client.py", line 1375, in getresponse
    response.begin()
  File "/usr/lib/python3.10/http/client.py", line 318, in begin
    version, status, reason = self._read_status()
  File "/usr/lib/python3.10/http/client.py", line 279, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
  File "/usr/lib/python3.10/socket.py", line 705, in readinto
    return self._sock.recv_into(b)
  File "/usr/lib/python3.10/ssl.py", line 1303, in recv_into
    return self.read(nbytes, buffer)
  File "/usr/lib/python3.10/ssl.py", line 1159, in read
    return self._sslobj.read(len, buffer)

2025-07-09 05:31:13,850 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=82f0f60debde4009dda1683c654b9947a58010878457cf59811606a125e51fed
2025-07-09 05:31:13,852 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=82f0f60debde4009dda1683c654b9947a58010878457cf59811606a125e51fed (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=82f0f60debde4009dda1683c654b9947a58010878457cf59811606a125e51fed)
2025-07-09 08:12:12,250 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 12 messages from #trade (keep_pinned: False)
2025-07-09 08:12:20,138 - __main__ - WARNING - Slow command execution: watchlist took 6.31s
2025-07-09 08:12:42,090 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 1/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-09 08:12:44,400 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 2/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-09 08:12:48,798 - handlers.discord.trading.advanced_commands - WARNING - Discord HTTP error (attempt 3/3): 404 Not Found (error code: 10008): Unknown Message
2025-07-09 08:12:48,802 - handlers.discord.trading.advanced_commands - ERROR - ❌ Failed to update status message after 3 attempts: 404 Not Found (error code: 10008): Unknown Message
2025-07-09 08:13:25,007 - __main__ - ERROR - Error stopping launchpad monitoring service: 'CryptoBot' object has no attribute 'launchpad_monitor'
2025-07-09 08:19:15,955 - __main__ - WARNING - Slow command execution: watchlist took 6.22s
2025-07-09 08:20:40,169 - __main__ - ERROR - Error stopping launchpad monitoring service: 'CryptoBot' object has no attribute 'launchpad_monitor'
2025-07-09 08:27:17,009 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #📈-market-data (keep_pinned: False)
2025-07-09 08:27:31,809 - __main__ - WARNING - Slow command execution: watchlist took 7.45s
2025-07-09 12:03:59,018 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5fa0b88d1c8d42e2366b97b69f40818ec5db442d4a2a8fe8eb0412847f7e09dd
2025-07-09 12:03:59,025 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5fa0b88d1c8d42e2366b97b69f40818ec5db442d4a2a8fe8eb0412847f7e09dd (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=5fa0b88d1c8d42e2366b97b69f40818ec5db442d4a2a8fe8eb0412847f7e09dd)
2025-07-09 12:08:20,284 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 63 messages from #time-trade (keep_pinned: False)
2025-07-09 12:26:01,855 - handlers.discord.alerts.trading_time_alerts - WARNING - Persistent market_session_status message not found, creating new one
2025-07-09 12:26:02,598 - handlers.discord.alerts.trading_time_alerts - WARNING - Persistent market_overview message not found, creating new one
2025-07-09 13:55:47,401 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=eda7918808a70253ee79ee5e43bda199a93e3d2704d8aa1bdde2de7bda0958b8
2025-07-09 13:55:47,406 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=eda7918808a70253ee79ee5e43bda199a93e3d2704d8aa1bdde2de7bda0958b8 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=eda7918808a70253ee79ee5e43bda199a93e3d2704d8aa1bdde2de7bda0958b8)
2025-07-09 13:55:56,114 - __main__ - WARNING - Slow command execution: watchlist took 15.00s
2025-07-09 13:59:02,711 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=81e014a8e19ced7547f3852ead7466ecb2ed2e8c60f0a927ebeaeb2791f4fb30
2025-07-09 13:59:02,713 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=81e014a8e19ced7547f3852ead7466ecb2ed2e8c60f0a927ebeaeb2791f4fb30 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=81e014a8e19ced7547f3852ead7466ecb2ed2e8c60f0a927ebeaeb2791f4fb30)
2025-07-09 17:34:08,211 - handlers.telegram.telegram_commands - ERROR - Error getting market indicators: 'MarketMonitorService' object has no attribute 'get_p2p_rates'
2025-07-09 17:58:10,771 - handlers.telegram.telegram_commands - WARNING - Failed to get XAU data: 'MT5DataService' object has no attribute 'get_instrument_data'
2025-07-09 17:59:18,322 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-09 17:59:20,260 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-09 18:22:34,482 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-09 18:22:34,524 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-09 18:23:28,627 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 30 messages from #📝-bot-logs (keep_pinned: True)
2025-07-09 18:26:53,241 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 58 messages from #time-trade (keep_pinned: False)
2025-07-09 18:27:28,954 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-09 19:22:29,295 - handlers.discord.alerts.trading_time_alerts - WARNING - Persistent market_session_status message not found, creating new one
2025-07-09 21:57:15,412 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-09 22:22:29,308 - handlers.discord.alerts.trading_time_alerts - WARNING - Persistent market_overview message not found, creating new one
2025-07-10 06:02:16,298 - services.market.trading_time_service - ERROR - Error getting news trading opportunities: 'NoneType' object has no attribute 'fetch_economic_calendar'
2025-07-10 06:04:06,829 - services.market.market_monitor_service - ERROR - Error fetching P2P rates: 
2025-07-10 06:04:10,098 - services.market.trading_time_service - ERROR - Error getting news trading opportunities: 'NoneType' object has no attribute 'fetch_economic_calendar'
2025-07-10 06:06:36,829 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3309a112dc352a271c26bd034db0781c6fbcda13c74ca8ad7a41be734be4ddab
2025-07-10 06:06:36,838 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3309a112dc352a271c26bd034db0781c6fbcda13c74ca8ad7a41be734be4ddab (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3309a112dc352a271c26bd034db0781c6fbcda13c74ca8ad7a41be734be4ddab)
2025-07-10 06:07:41,617 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8df8f2325c90dc4ac5b92b276b1775a4c56ab04c0b7c16a88f9f3ac0aef3c992
2025-07-10 06:07:41,620 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8df8f2325c90dc4ac5b92b276b1775a4c56ab04c0b7c16a88f9f3ac0aef3c992 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8df8f2325c90dc4ac5b92b276b1775a4c56ab04c0b7c16a88f9f3ac0aef3c992)
2025-07-10 06:16:13,372 - services.market.trading_time_service - ERROR - Error getting news trading opportunities: 'NoneType' object has no attribute 'fetch_economic_calendar'
2025-07-10 06:23:17,129 - services.market.trading_time_service - ERROR - Error getting news trading opportunities: 'NoneType' object has no attribute 'fetch_economic_calendar'
2025-07-10 06:23:17,852 - services.market.economic_calendar_service - ERROR - Forex Factory API error: 429
2025-07-10 06:23:19,332 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:19,335 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:23,560 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:23,562 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:24,788 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:24,790 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:29,494 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:29,497 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:31,227 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:31,227 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:36,685 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:36,686 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:39,187 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:39,187 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:42,902 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:42,906 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:46,513 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:46,515 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:52,236 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:52,238 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:57,968 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:23:57,972 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:06,246 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:06,248 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:14,528 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:14,531 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:26,621 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:26,624 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:30,860 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:30,862 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:48,631 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:48,635 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:52,864 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:24:52,868 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:19,178 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:19,182 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:23,413 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:23,418 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:54,071 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:54,074 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:58,283 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:25:58,286 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:26:28,944 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:26:28,949 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:26:33,157 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:26:33,162 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:27:03,853 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:27:03,857 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:27:11,567 - telegram.ext.Updater - ERROR - Error while getting Updates: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-10 06:27:11,576 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 384, in polling_action_cb
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 381, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
